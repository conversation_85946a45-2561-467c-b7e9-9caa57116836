package main

import (
	"encoding/json"
	"fmt"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"

	"gorm.io/datatypes"
)

// 模拟parseMarkConfig函数
func parseMarkConfig(markConfigJSON datatypes.JSON) (*sandbox_api_model.MarkConfig, error) {
	if len(markConfigJSON) == 0 {
		return nil, nil
	}

	var markConfig map[string]interface{}
	var markConfigStruct sandbox_api_model.MarkConfig

	// 先解析为map
	if err := json.Unmarshal(markConfigJSON, &markConfig); err != nil {
		return nil, err
	}

	// 检查是否为空的JSON对象或所有字段都为零值
	isEmpty := true
	for key, value := range markConfig {
		switch key {
		case "uuid", "name", "color":
			if str, ok := value.(string); ok && str != "" {
				isEmpty = false
				break
			}
		case "x", "y", "width", "height":
			if num, ok := value.(float64); ok && num != 0 {
				isEmpty = false
				break
			}
		}
		if !isEmpty {
			break
		}
	}

	// 如果所有字段都为零值，返回nil
	if isEmpty {
		return nil, nil
	}

	// 将map转换为结构体
	configBytes, err := json.Marshal(markConfig)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(configBytes, &markConfigStruct); err != nil {
		return nil, err
	}

	return &markConfigStruct, nil
}

func main() {
	// 测试当数据库中mark_config为null时的情况
	var nullJSON datatypes.JSON

	fmt.Printf("=== 测试null JSON ===\n")
	fmt.Printf("null JSON length: %d\n", len(nullJSON))
	fmt.Printf("null JSON is nil: %v\n", nullJSON == nil)

	result1, err := parseMarkConfig(nullJSON)
	fmt.Printf("parseMarkConfig(null) result: %v, error: %v\n", result1, err)

	// 测试零值JSON对象的情况
	zeroValueJSONBytes := []byte(`{"uuid":"","x":0,"y":0,"width":0,"height":0,"name":"","color":""}`)
	var zeroValueJSON datatypes.JSON = zeroValueJSONBytes

	fmt.Printf("\n=== 测试零值JSON ===\n")
	fmt.Printf("zero value JSON length: %d\n", len(zeroValueJSON))
	fmt.Printf("zero value JSON value: %s\n", string(zeroValueJSON))

	result2, err := parseMarkConfig(zeroValueJSON)
	fmt.Printf("parseMarkConfig(zeroValue) result: %v, error: %v\n", result2, err)

	// 测试有效JSON对象的情况
	validJSONBytes := []byte(`{"uuid":"test-uuid","x":10,"y":20,"width":100,"height":50,"name":"test","color":"red"}`)
	var validJSON datatypes.JSON = validJSONBytes

	fmt.Printf("\n=== 测试有效JSON ===\n")
	fmt.Printf("valid JSON value: %s\n", string(validJSON))

	result3, err := parseMarkConfig(validJSON)
	fmt.Printf("parseMarkConfig(valid) result: %+v, error: %v\n", result3, err)

	// 测试JSON序列化结果
	fmt.Printf("\n=== 测试JSON序列化 ===\n")

	// nil指针序列化
	nilMarkConfig := (*sandbox_api_model.MarkConfig)(nil)
	nilBytes, _ := json.Marshal(nilMarkConfig)
	fmt.Printf("json.Marshal(nil): %s\n", string(nilBytes))

	// 零值结构体序列化
	zeroMarkConfig := &sandbox_api_model.MarkConfig{}
	zeroBytes, _ := json.Marshal(zeroMarkConfig)
	fmt.Printf("json.Marshal(zeroStruct): %s\n", string(zeroBytes))
}
