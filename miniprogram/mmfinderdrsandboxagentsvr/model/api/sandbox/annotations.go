// Package sandbox 定义沙箱标注相关的接口类型
package sandbox

// MarkConfig 标注配置
type MarkConfig struct {
	UUID   string  `json:"uuid"`
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Name   string  `json:"name"`
	Color  string  `json:"color"`
}

// Screen 屏幕信息
type Screen struct {
	ScreenWidth  int `json:"screenWidth"`
	ScreenHeight int `json:"screenHeight"`
}

// CreateSandboxReq 创建沙箱请求
type CreateSandboxReq struct {
	RTX         string `json:"rtx"`
	AppID       string `json:"appId"`
	AppEntryURL string `json:"appEntryUrl"`
	UserID      string `json:"userId"`
	AuthCode    string `json:"authCode,omitempty"`
}

// CreateSandboxRespData 创建沙箱响应数据
type CreateSandboxRespData struct {
	URL              string `json:"url"`
	DomXML           string `json:"domXML"`
	AllElementsRects string `json:"allElementsRects"`
	ScreenWidth      int    `json:"screenWidth"`
	ScreenHeight     int    `json:"screenHeight"`
	TargetID         string `json:"targetId"`
}

// ActionReq 沙箱交互步骤请求
type ActionReq struct {
	RTX        string      `json:"rtx"`
	AppID      string      `json:"appId"`
	UserID     string      `json:"userId"`
	TargetID   string      `json:"targetId"`
	Type       string      `json:"type"`
	MarkConfig *MarkConfig `json:"markConfig,omitempty"`
	Xpath      string      `json:"xpath,omitempty"`
	Screen     *Screen     `json:"screen,omitempty"`
	Text       string      `json:"text,omitempty"`
	Direction  string      `json:"direction,omitempty"`
	Length     int         `json:"length,omitempty"`
}

// ActionRespData 沙箱交互步骤响应数据
type ActionRespData struct {
	URL              string `json:"url"`
	DomXML           string `json:"domXML"`
	AllElementsRects string `json:"allElementsRects"`
	ScreenWidth      int    `json:"screenWidth"`
	ScreenHeight     int    `json:"screenHeight"`
}

// KillSandboxReq 关闭沙箱请求
type KillSandboxReq struct {
	RTX      string `json:"rtx"`
	AppID    string `json:"appId"`
	TargetID string `json:"targetId"`
	UserID   string `json:"userId"`
}

// Operation 操作记录
type Operation struct {
	ID               int64                  `json:"id"`
	IsDeleted        int8                   `json:"isDeleted"`
	Type             string                 `json:"type"`
	MarkConfig       *MarkConfig            `json:"markConfig,omitempty"`
	Xpath            string                 `json:"xpath,omitempty"`
	Text             string                 `json:"text,omitempty"`
	Direction        string                 `json:"direction,omitempty"`
	Length           int                    `json:"length,omitempty"`
	URL              string                 `json:"url,omitempty"`
	DomXML           string                 `json:"domXML,omitempty"`
	AllElementsRects string                 `json:"allElementsRects,omitempty"`
	ScreenWidth      int                    `json:"screenWidth,omitempty"`
	ScreenHeight     int                    `json:"screenHeight,omitempty"`
	ExtraInfo        map[string]interface{} `json:"extraInfo,omitempty"`
}

// SubmitAnnotationReq 提交标注内容请求
type SubmitAnnotationReq struct {
	RTX         string      `json:"rtx"`
	AppID       string      `json:"appId"`
	TargetID    string      `json:"targetId"`
	UserID      string      `json:"userId"`
	Instruction string      `json:"instruction"`
	SubmitTime  string      `json:"submitTime"`
	Operations  []Operation `json:"operations"`
	Source      int8        `json:"source,omitempty"`
}

// Annotation 标注记录
type Annotation struct {
	ID          int64       `json:"id"`
	RTX         string      `json:"rtx"`
	AppID       string      `json:"appId"`
	IsDeleted   int8        `json:"isDeleted"`
	TargetID    string      `json:"targetId"`
	UserID      string      `json:"userId"`
	Instruction string      `json:"instruction"`
	Note        string      `json:"note"`
	SubmitTime  string      `json:"submitTime"`
	Operations  []Operation `json:"operations"`
	IsEval      int8        `json:"isEval"`
	Source      int8        `json:"source"`
	IsReady     int8        `json:"isReady"`
}

// GetAnnotationsRespData 获取标注内容列表响应数据
type GetAnnotationsRespData struct {
	Total       int          `json:"total"`
	Annotations []Annotation `json:"annotations"`
}

// DeleteAnnotationReq 删除标注内容请求
type DeleteAnnotationReq struct {
	AnnotationID int64 `json:"annotationId"` // 标注记录ID
}

// EvalAnnotationReq 将标注数据置为评估集请求
type EvalAnnotationReq struct {
	AnnotationID int64 `json:"annotationId"` // 标注记录ID
}

// DeleteAnnotationOperationReq 删除标注操作记录请求
type DeleteAnnotationOperationReq struct {
	AnnotationID int64 `json:"annotationId"` // 标注记录ID
	OperationID  int64 `json:"operationId"`  // 标注操作记录ID
}

// UpdateAnnotationOperationReq 更新标注操作记录请求
type UpdateAnnotationOperationReq struct {
	AnnotationID int64       `json:"annotationId"` // 标注记录ID
	OperationID  int64       `json:"operationId"`  // 标注操作记录ID
	Text         string      `json:"text"`
	Direction    string      `json:"direction"`
	Length       int         `json:"length"`
	Type         string      `json:"type"`       // 操作类型
	MarkConfig   *MarkConfig `json:"markConfig"` // 标注配置
	Xpath        string      `json:"xpath"`      // XPath 路径
}

// UpdateAnnotationReq 更新标注内容请求
type UpdateAnnotationReq struct {
	AnnotationID int64  `json:"annotationId"` // 标注记录ID
	Note         string `json:"note"`         // 标注备注
}

// ReadyAnnotationReq 标注 ready 请求
type ReadyAnnotationReq struct {
	AnnotationID int64 `json:"annotationId"` // 标注记录ID
}
